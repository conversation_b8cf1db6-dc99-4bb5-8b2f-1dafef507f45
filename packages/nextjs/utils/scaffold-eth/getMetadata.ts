import { getCurrentTimestamp } from "@/utils";
import type { Metadata } from "next";

const baseUrl = process.env.NEXT_PUBLIC_META_IMAGE_SOURCE
  ? process.env.NEXT_PUBLIC_META_IMAGE_SOURCE
  : `http://localhost:${process.env.PORT || 3000}`;
const titleTemplate = "%s | PredictOne";
const timeStamp = getCurrentTimestamp();
export const getMetadata = ({
  title,
  description,
  imageRelativePath,
}: {
  title: string;
  description: string;
  imageRelativePath?: string;
}): Metadata => {
  const imageUrl = `${baseUrl}${imageRelativePath}?stamp=${timeStamp}`;

  return {
    metadataBase: new URL(baseUrl),
    title: {
      default: title,
      template: titleTemplate,
    },
    description: description,
    openGraph: {
      title: {
        default: title,
        template: titleTemplate,
      },
      description: description,
      images: [
        {
          url: imageUrl,
        },
      ],
    },
    twitter: {
      title: {
        default: title,
        template: titleTemplate,
      },
      card: "summary_large_image",
      description: description,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 628,
          alt: title,
        },
      ],
    },
    // 添加额外的 Twitter meta 标签以确保兼容性
    other: {
      "twitter:image:src": imageUrl,
    },
    icons: {
      icon: [{ url: "/logo.png", sizes: "32x32", type: "image/png" }],
    },
  };
};
