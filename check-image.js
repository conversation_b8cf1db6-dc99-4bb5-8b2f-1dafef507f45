#!/usr/bin/env node

/**
 * 图片检查脚本 - 模拟 checkImageExists 函数
 * 使用方法: node check-image.js [slug]
 * 例如: node check-image.js "<PERSON>_Powell_1754299858332-en"
 */

const https = require('https');
const http = require('http');

// 模拟 checkImageExists 函数
async function checkImageExists(url) {
  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'HEAD',
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; ImageChecker/1.0)',
          'Accept': '*/*'
        }
      };

      const req = client.request(options, (res) => {
        const exists = res.statusCode >= 200 && res.statusCode < 300;
        console.log(`  Status: ${res.statusCode} ${exists ? '✅' : '❌'}`);
        resolve(exists);
      });

      req.on('error', (error) => {
        console.log(`  Error: ${error.message} ❌`);
        resolve(false);
      });

      req.on('timeout', () => {
        console.log(`  Timeout ❌`);
        req.destroy();
        resolve(false);
      });

      req.end();
    } catch (error) {
      console.log(`  Parse Error: ${error.message} ❌`);
      resolve(false);
    }
  });
}

// 处理 slug 的函数（模拟 layout.tsx 的逻辑）
function processSlug(slug) {
  const result = slug
    .split("")
    .filter(char => /[a-zA-Z0-9\s-]/.test(char))
    .join("");
  
  return result.replace(/\s+/g, "-");
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('使用方法: node check-image.js [slug]');
    console.log('例如: node check-image.js "Jerome_Powell_1754299858332-en"');
    process.exit(1);
  }
  
  const inputSlug = args[0];
  const processedSlug = processSlug(inputSlug);
  
  console.log(`原始 slug: ${inputSlug}`);
  console.log(`处理后 slug: ${processedSlug}`);
  console.log('');
  
  const baseUrl = 'https://sgp1.vultrobjects.com';
  const manualImagePath = `/predict-pictures/pics/${processedSlug}_manual.png`;
  const autoImagePath = `/predict-pictures/pics/${processedSlug}.png`;
  const defaultImagePath = `/predict-pictures/pics/Boss_titan.png`;
  
  console.log('🔍 检查图片存在性...\n');
  
  // 检查 manual 图片
  console.log(`1. Manual 图片:`);
  console.log(`   ${baseUrl}${manualImagePath}`);
  const manualExists = await checkImageExists(baseUrl + manualImagePath);
  console.log('');
  
  // 检查 auto 图片
  console.log(`2. Auto 图片:`);
  console.log(`   ${baseUrl}${autoImagePath}`);
  const autoExists = await checkImageExists(baseUrl + autoImagePath);
  console.log('');
  
  // 检查默认图片
  console.log(`3. 默认图片:`);
  console.log(`   ${baseUrl}${defaultImagePath}`);
  const defaultExists = await checkImageExists(baseUrl + defaultImagePath);
  console.log('');
  
  // 应用选择逻辑
  console.log('📋 选择逻辑:');
  let selectedImagePath;
  
  if (processedSlug && manualExists) {
    selectedImagePath = manualImagePath;
    console.log('   ✅ 选择: Manual 图片');
  } else {
    selectedImagePath = autoImagePath;
    console.log('   📄 选择: Auto 图片');
  }
  
  console.log('');
  console.log('🎯 最终结果:');
  console.log(`   图片路径: ${baseUrl}${selectedImagePath}`);
  console.log(`   Manual存在: ${manualExists ? '✅' : '❌'}`);
  console.log(`   Auto存在: ${autoExists ? '✅' : '❌'}`);
  console.log(`   默认存在: ${defaultExists ? '✅' : '❌'}`);
}

// 运行脚本
main().catch(error => {
  console.error('❌ 脚本执行失败:', error);
  process.exit(1);
});
